'use client';
import React, { forwardRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  PressableProps,
  Text,
} from 'react-native';
import {
  MaterialSymbol,
  MaterialSymbolName,
} from '@/lib/icons/material-symbols';
import { useTheme } from '@/lib/theme/theme-provider';

// Material Design 3 Button 变体
type M3ButtonVariant =
  | 'filled' // Filled Button
  | 'tonal' // Filled Tonal Button
  | 'outlined' // Outlined Button
  | 'text' // Text Button
  | 'elevated'; // Elevated Button

// Material Design 3 Button 尺寸
type M3ButtonSize = 'small' | 'medium' | 'large';

export interface M3ButtonProps extends Omit<PressableProps, 'style'> {
  variant?: M3ButtonVariant;
  size?: M3ButtonSize;
  children?: React.ReactNode;
  icon?: MaterialSymbolName;
  iconPosition?: 'leading' | 'trailing';
  fullWidth?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  onPress?: () => void;
}

const M3Button = forwardRef<TouchableOpacity, M3ButtonProps>(
  (
    {
      variant = 'filled',
      size = 'medium',
      children,
      icon,
      iconPosition = 'leading',
      fullWidth = false,
      loading = false,
      disabled = false,
      style,
      textStyle,
      onPress,
      ...props
    },
    ref
  ) => {
    const { themeColors, isDark } = useTheme();
    const [isPressed, setIsPressed] = useState(false);

    // M3 Button 官方规范尺寸
    const getSizeSpecs = () => {
      switch (size) {
        case 'small':
          return {
            height: 32,
            paddingHorizontal: 16,
            fontSize: 12,
            iconSize: 16,
            borderRadius: 16,
          };
        case 'medium':
          return {
            height: 40,
            paddingHorizontal: 24,
            fontSize: 14,
            iconSize: 18,
            borderRadius: 20,
          };
        case 'large':
          return {
            height: 48,
            paddingHorizontal: 32,
            fontSize: 16,
            iconSize: 20,
            borderRadius: 24,
          };
        default:
          return {
            height: 40,
            paddingHorizontal: 24,
            fontSize: 14,
            iconSize: 18,
            borderRadius: 20,
          };
      }
    };

    const sizeSpecs = getSizeSpecs();

    // M3 Button 颜色规范
    const getColors = () => {
      const baseOpacity = disabled ? 0.38 : isPressed ? 0.12 : 1;

      switch (variant) {
        case 'filled':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)' // on-surface/12%
                : 'rgba(28, 27, 31, 0.12)' // on-surface/12%
              : themeColors.primary.main,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)' // on-surface/38%
                : 'rgba(28, 27, 31, 0.38)' // on-surface/38%
              : themeColors.primary.on,
            elevation: disabled ? 0 : 1,
            borderWidth: 0,
          };

        case 'tonal':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)'
                : 'rgba(28, 27, 31, 0.12)'
              : themeColors.secondary.container,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : themeColors.secondary.onContainer,
            elevation: 0,
            borderWidth: 0,
          };

        case 'outlined':
          return {
            backgroundColor: 'transparent',
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : themeColors.primary.main,
            elevation: 0,
            borderWidth: 1,
            borderColor: disabled
              ? isDark
                ? 'rgba(202, 196, 208, 0.12)' // outline/12%
                : 'rgba(121, 116, 126, 0.12)' // outline/12%
              : themeColors.outline.main,
          };

        case 'text':
          return {
            backgroundColor: 'transparent',
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : themeColors.primary.main,
            elevation: 0,
            borderWidth: 0,
          };

        case 'elevated':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)'
                : 'rgba(28, 27, 31, 0.12)'
              : themeColors.surface.containerLow,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : themeColors.primary.main,
            elevation: disabled ? 0 : 1,
            borderWidth: 0,
            shadowColor: '#000000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 3,
          };

        default:
          return {
            backgroundColor: themeColors.primary.main,
            textColor: themeColors.primary.on,
            elevation: 1,
            borderWidth: 0,
          };
      }
    };

    const colors = getColors();

    // 按钮样式
    const buttonStyles: ViewStyle = {
      height: sizeSpecs.height,
      paddingHorizontal: sizeSpecs.paddingHorizontal,
      borderRadius: sizeSpecs.borderRadius,
      backgroundColor: colors.backgroundColor,
      borderWidth: colors.borderWidth,
      borderColor: colors.borderColor,
      elevation: colors.elevation,
      shadowColor: colors.shadowColor,
      shadowOffset: colors.shadowOffset,
      shadowOpacity: colors.shadowOpacity,
      shadowRadius: colors.shadowRadius,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: icon ? 8 : 0,
      opacity: isPressed ? 0.8 : 1,
      ...(fullWidth && { width: '100%' }),
    };

    // 文本样式
    const finalTextStyles: TextStyle = {
      fontSize: sizeSpecs.fontSize,
      fontWeight: '500',
      color: colors.textColor,
      fontFamily: 'roboto-medium', // 使用现有的字体
      textAlign: 'center',
      ...textStyle,
    };

    const handlePressIn = () => {
      if (!disabled && !loading) {
        setIsPressed(true);
      }
    };

    const handlePressOut = () => {
      if (!disabled && !loading) {
        setIsPressed(false);
      }
    };

    const handlePress = () => {
      if (!disabled && !loading) {
        onPress?.();
      }
    };

    return (
      <TouchableOpacity
        ref={ref}
        style={[buttonStyles, style]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled || loading}
        activeOpacity={0.7}
        {...props}
      >
        {/* Leading Icon */}
        {icon && iconPosition === 'leading' && !loading && (
          <MaterialSymbol
            name={icon}
            size={sizeSpecs.iconSize}
            color={colors.textColor}
            weight="normal"
          />
        )}

        {/* Loading Spinner */}
        {loading && (
          <View
            style={{
              width: sizeSpecs.iconSize,
              height: sizeSpecs.iconSize,
              borderRadius: sizeSpecs.iconSize / 2,
              borderWidth: 2,
              borderColor: colors.textColor,
              borderTopColor: 'transparent',
            }}
          />
        )}

        {/* Button Text */}
        {children && <Text style={finalTextStyles}>{children}</Text>}

        {/* Trailing Icon */}
        {icon && iconPosition === 'trailing' && !loading && (
          <MaterialSymbol
            name={icon}
            size={sizeSpecs.iconSize}
            color={colors.textColor}
            weight="normal"
          />
        )}
      </TouchableOpacity>
    );
  }
);

M3Button.displayName = 'M3Button';

export default M3Button;
