'use client';
import React from 'react';
import { View } from 'react-native';
import {
  M3Button,
  M3ButtonText,
  M3ButtonIcon,
  M3ButtonSpinner,
} from './material3-button';
import { ThemeProvider, useTheme } from '@/lib/theme/theme-provider';

// Material 3 Button 使用示例组件
export function M3ButtonExamples() {
  const { theme, toggleTheme } = useTheme();

  return (
    <View className="p-6 space-y-6 bg-m3-background-main min-h-screen">
      {/* 主题切换按钮 */}
      <View className="flex-row justify-center">
        <M3Button variant="outlined" onPress={toggleTheme}>
          <M3ButtonText>
            切换到 {theme === 'light' ? '深色' : '浅色'} 模式
          </M3ButtonText>
        </M3Button>
      </View>

      {/* Filled Buttons */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          Filled Buttons
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="filled" size="small">
            <M3ButtonText>小号按钮</M3ButtonText>
          </M3Button>

          <M3Button variant="filled" size="medium">
            <M3ButtonText>中号按钮</M3ButtonText>
          </M3Button>

          <M3Button variant="filled" size="large">
            <M3ButtonText>大号按钮</M3ButtonText>
          </M3Button>
        </View>

        {/* 带图标的按钮 */}
        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="filled" iconPosition="leading">
            <M3ButtonIcon as="div" className="text-xs">
              ▶
            </M3ButtonIcon>
            <M3ButtonText>播放</M3ButtonText>
          </M3Button>

          <M3Button variant="filled" iconPosition="trailing">
            <M3ButtonText>下载</M3ButtonText>
            <M3ButtonIcon as="div" className="text-xs">
              ⬇
            </M3ButtonIcon>
          </M3Button>
        </View>

        {/* 加载状态 */}
        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="filled" loading={true}>
            <M3ButtonSpinner />
            <M3ButtonText>加载中...</M3ButtonText>
          </M3Button>

          <M3Button variant="filled" disabled={true}>
            <M3ButtonText>禁用状态</M3ButtonText>
          </M3Button>
        </View>
      </View>

      {/* Filled Tonal Buttons */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          Filled Tonal Buttons
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="filled-tonal" size="small">
            <M3ButtonText>小号</M3ButtonText>
          </M3Button>

          <M3Button variant="filled-tonal" size="medium">
            <M3ButtonText>中号</M3ButtonText>
          </M3Button>

          <M3Button variant="filled-tonal" size="large">
            <M3ButtonText>大号</M3ButtonText>
          </M3Button>
        </View>
      </View>

      {/* Outlined Buttons */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          Outlined Buttons
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="outlined">
            <M3ButtonText>轮廓按钮</M3ButtonText>
          </M3Button>

          <M3Button variant="outlined" iconPosition="leading">
            <M3ButtonIcon as="div" className="text-xs">
              ⚙
            </M3ButtonIcon>
            <M3ButtonText>设置</M3ButtonText>
          </M3Button>
        </View>
      </View>

      {/* Text Buttons */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          Text Buttons
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="text">
            <M3ButtonText>文本按钮</M3ButtonText>
          </M3Button>

          <M3Button variant="text">
            <M3ButtonText>取消</M3ButtonText>
          </M3Button>

          <M3Button variant="text">
            <M3ButtonText>确定</M3ButtonText>
          </M3Button>
        </View>
      </View>

      {/* Elevated Buttons */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          Elevated Buttons
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="elevated">
            <M3ButtonText>悬浮按钮</M3ButtonText>
          </M3Button>

          <M3Button variant="elevated" iconPosition="leading">
            <M3ButtonIcon as="div" className="text-xs">
              ➕
            </M3ButtonIcon>
            <M3ButtonText>添加</M3ButtonText>
          </M3Button>
        </View>
      </View>

      {/* 交互状态示例 */}
      <View className="space-y-4">
        <M3ButtonText className="text-lg font-semibold text-m3-surface-on">
          交互状态
        </M3ButtonText>

        <View className="flex-row flex-wrap gap-3">
          <M3Button variant="filled" state="hovered">
            <M3ButtonText>悬停状态</M3ButtonText>
          </M3Button>

          <M3Button variant="outlined" state="pressed">
            <M3ButtonText>按下状态</M3ButtonText>
          </M3Button>

          <M3Button variant="filled-tonal" state="focused">
            <M3ButtonText>聚焦状态</M3ButtonText>
          </M3Button>
        </View>
      </View>
    </View>
  );
}

// 包装组件，提供主题支持
export function M3ButtonExamplesWithTheme() {
  return (
    <ThemeProvider>
      <M3ButtonExamples />
    </ThemeProvider>
  );
}

export default M3ButtonExamplesWithTheme;
